import { useEffect } from 'react';
import { Routes, Route } from 'react-router-dom';
import { AppProvider } from './context/AppContext';
import { SplitContextProvider } from './context/SplitAppContext';
import { PerformanceProvider } from './context/PerformanceContext';
import { ToastProvider } from './components/ui/ToastContainer';
import Layout from './components/Layout';
import { LoadingSpinner } from './components/ui/LoadingSpinner';
import { OfflineIndicator } from './components/ui/OfflineIndicator';
import OfflineDebugger from './components/OfflineDebugger';
import { LazyWrapper } from './components/ui/LazyWrapper';
import { AppErrorBoundary } from './components/error/AppErrorBoundary';
import { useTouchOptimization } from './hooks/useTouchOptimization';
import { useServiceWorker } from './hooks/useOffline';
import { useEnhancedMemoryManagement } from './hooks/useEnhancedMemoryManagement';

import { createLazyComponent, PreloadStrategy } from './utils/lazyLoading';

// Enhanced lazy loading with retry logic and error handling
const AppGenerator = createLazyComponent(
  () => import('./components/AppGenerator'),
  { retries: 3, retryDelay: 1000 }
);

const AppGeneratorResults = createLazyComponent(
  () => import('./components/AppGeneratorResults'),
  { retries: 3, retryDelay: 1000 }
);

const AIChatInterface = createLazyComponent(
  () => import('./components/AIChatInterface'),
  { retries: 3, retryDelay: 1000 }
);

const WebsiteGenerator = createLazyComponent(
  () => import('./components/WebsiteGenerator'),
  { retries: 3, retryDelay: 1000 }
);

const MobileAppBuilder = createLazyComponent(
  () => import('./components/MobileAppBuilder'),
  { retries: 3, retryDelay: 1000 }
);

const FormValidation = createLazyComponent(
  () => import('./components/FormValidation'),
  { retries: 3, retryDelay: 1000 }
);

const FormValidationDemo = createLazyComponent(
  () => import('./components/FormValidationDemo'),
  { retries: 3, retryDelay: 1000 }
);

// Heavy components with more aggressive lazy loading
const MobilePerformanceDemo = createLazyComponent(
  () => import('./components/MobilePerformanceDemo'),
  { retries: 2, retryDelay: 1500 }
);

const MemoryManagementDemo = createLazyComponent(
  () => import('./components/MemoryManagementDemo'),
  { retries: 2, retryDelay: 1500 }
);

// Initialize preload strategy
const preloadStrategy = new PreloadStrategy();

function App() {
  // Initialize memory management
  const { addCleanup } = useEnhancedMemoryManagement();
  
  // Initialize touch optimizations
  useTouchOptimization();
  
  // Initialize service worker
  useServiceWorker();

  useEffect(() => {
    // Add mobile-specific CSS classes
    const isMobile = window.innerWidth < 768;
    if (isMobile) {
      document.body.classList.add('mobile-optimized');
    }

    // Optimize for mobile performance
    if ('ontouchstart' in window) {
      document.body.classList.add('touch-device');
    }

    // Add CSS for touch feedback and mobile optimizations
    const style = document.createElement('style');
    style.textContent = `
      .mobile-optimized {
        -webkit-tap-highlight-color: transparent;
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        user-select: none;
      }
      
      .touch-device * {
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
      }
      
      .touch-feedback {
        transform: scale(0.95);
        transition: transform 0.1s ease-out;
      }
      
      /* Optimize animations for mobile */
      @media (max-width: 768px) {
        * {
          animation-duration: 0.2s !important;
          transition-duration: 0.2s !important;
        }
      }
      
      /* Reduce motion for users who prefer it */
      @media (prefers-reduced-motion: reduce) {
        * {
          animation-duration: 0.01ms !important;
          animation-iteration-count: 1 !important;
          transition-duration: 0.01ms !important;
        }
      }
      
      /* Hardware acceleration for better performance */
      .will-change-transform {
        will-change: transform;
      }
      
      .will-change-opacity {
        will-change: opacity;
      }
      
      /* Optimize scrolling */
      .scroll-smooth {
        -webkit-overflow-scrolling: touch;
        scroll-behavior: smooth;
      }
    `;
    document.head.appendChild(style);

    // Register cleanup with memory manager
    addCleanup(() => {
      if (document.head.contains(style)) {
        document.head.removeChild(style);
      }
      // Clean up CSS classes
      document.body.classList.remove('mobile-optimized', 'touch-device');
    });

    return () => {
      if (document.head.contains(style)) {
        document.head.removeChild(style);
      }
      // Clean up CSS classes
      document.body.classList.remove('mobile-optimized', 'touch-device');
      // Clean up preload strategy
      preloadStrategy.cleanup();
    };
  }, [addCleanup]); // Proper dependency array

  return (
    <AppErrorBoundary>
      <AppProvider>
        <SplitContextProvider>
          <PerformanceProvider>
            <ToastProvider>
              <Layout>
                <Routes>
            <Route 
              path="/" 
              element={
                <LazyWrapper
                  fallback={
                    <div className="flex items-center justify-center min-h-screen">
                      <LoadingSpinner size="lg" />
                    </div>
                  }
                >
                  <AppGenerator />
                </LazyWrapper>
              } 
            />
            <Route 
              path="/results/:projectId" 
              element={
                <LazyWrapper
                  fallback={
                    <div className="flex items-center justify-center min-h-screen">
                      <LoadingSpinner size="lg" />
                    </div>
                  }
                >
                  <AppGeneratorResults />
                </LazyWrapper>
              } 
            />
            <Route 
              path="/chat/:projectId" 
              element={
                <LazyWrapper
                  fallback={
                    <div className="flex items-center justify-center min-h-screen">
                      <LoadingSpinner size="lg" />
                    </div>
                  }
                >
                  <AIChatInterface />
                </LazyWrapper>
              } 
            />
            <Route 
              path="/generate" 
              element={
                <LazyWrapper>
                  <WebsiteGenerator />
                </LazyWrapper>
              } 
            />
            <Route 
              path="/build" 
              element={
                <LazyWrapper>
                  <MobileAppBuilder />
                </LazyWrapper>
              } 
            />
            <Route 
              path="/form-validation" 
              element={
                <LazyWrapper>
                  <FormValidation />
                </LazyWrapper>
              } 
            />
            <Route 
              path="/form-demo" 
              element={
                <LazyWrapper>
                  <FormValidationDemo />
                </LazyWrapper>
              } 
            />
            <Route 
              path="/mobile-performance" 
              element={
                <LazyWrapper
                  fallback={
                    <div className="flex items-center justify-center min-h-screen">
                      <div className="text-center">
                        <LoadingSpinner size="lg" />
                        <p className="mt-4 text-gray-400">Loading performance demo...</p>
                      </div>
                    </div>
                  }
                >
                  <MobilePerformanceDemo />
                </LazyWrapper>
              } 
            />
            <Route 
              path="/memory-management" 
              element={
                <LazyWrapper
                  fallback={
                    <div className="flex items-center justify-center min-h-screen">
                      <div className="text-center">
                        <LoadingSpinner size="lg" />
                        <p className="mt-4 text-gray-400">Loading memory management demo...</p>
                      </div>
                    </div>
                  }
                >
                  <MemoryManagementDemo />
                </LazyWrapper>
              } 
            />
                </Routes>
              </Layout>
              <OfflineIndicator />
              <OfflineDebugger />
            </ToastProvider>
          </PerformanceProvider>
        </SplitContextProvider>
      </AppProvider>
    </AppErrorBoundary>
  );
}

export default App;