import { lazy, ComponentType, LazyExoticComponent } from 'react';

// Enhanced lazy loading with error handling and retry logic
export function createLazyComponent<T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  options: {
    retries?: number;
    retryDelay?: number;
    fallback?: ComponentType;
  } = {}
): LazyExoticComponent<T> {
  const { retries = 3, retryDelay = 1000 } = options;

  const retryImport = async (attempt = 0): Promise<{ default: T }> => {
    try {
      return await importFn();
    } catch (error) {
      if (attempt < retries) {
        await new Promise(resolve => setTimeout(resolve, retryDelay));
        return retryImport(attempt + 1);
      }
      throw error;
    }
  };

  return lazy(() => retryImport());
}

// Progressive loading for large datasets
export class ProgressiveLoader<T> {
  private items: T[] = [];
  private batchSize: number;
  private loadedBatches = 0;
  private totalBatches: number;
  private onProgress?: (progress: number) => void;

  constructor(
    private data: T[],
    batchSize = 50,
    onProgress?: (progress: number) => void
  ) {
    this.batchSize = batchSize;
    this.totalBatches = Math.ceil(data.length / batchSize);
    this.onProgress = onProgress;
  }

  async loadNextBatch(): Promise<T[]> {
    if (this.loadedBatches >= this.totalBatches) {
      return [];
    }

    const startIndex = this.loadedBatches * this.batchSize;
    const endIndex = Math.min(startIndex + this.batchSize, this.data.length);
    const batch = this.data.slice(startIndex, endIndex);

    // Simulate async loading with requestIdleCallback for better performance
    return new Promise((resolve) => {
      const callback = () => {
        this.items.push(...batch);
        this.loadedBatches++;
        
        const progress = (this.loadedBatches / this.totalBatches) * 100;
        this.onProgress?.(progress);
        
        resolve(batch);
      };

      if ('requestIdleCallback' in window) {
        requestIdleCallback(callback);
      } else {
        setTimeout(callback, 0);
      }
    });
  }

  async loadAll(): Promise<T[]> {
    while (this.loadedBatches < this.totalBatches) {
      await this.loadNextBatch();
    }
    return this.items;
  }

  getLoadedItems(): T[] {
    return [...this.items];
  }

  getProgress(): number {
    return (this.loadedBatches / this.totalBatches) * 100;
  }

  hasMore(): boolean {
    return this.loadedBatches < this.totalBatches;
  }

  reset(): void {
    this.items = [];
    this.loadedBatches = 0;
  }
}

// Virtual scrolling for large lists
export interface VirtualScrollOptions {
  itemHeight: number;
  containerHeight: number;
  overscan?: number;
}

export function calculateVirtualItems<T>(
  items: T[],
  scrollTop: number,
  options: VirtualScrollOptions
) {
  const { itemHeight, containerHeight, overscan = 5 } = options;
  
  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
  const endIndex = Math.min(
    items.length - 1,
    Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
  );

  const visibleItems = items.slice(startIndex, endIndex + 1).map((item, index) => ({
    item,
    index: startIndex + index,
    offsetTop: (startIndex + index) * itemHeight,
  }));

  return {
    visibleItems,
    totalHeight: items.length * itemHeight,
    startIndex,
    endIndex,
  };
}

// Intersection Observer for lazy loading images and components
export function createIntersectionObserver(
  callback: (entries: IntersectionObserverEntry[]) => void,
  options: IntersectionObserverInit = {}
): IntersectionObserver {
  const defaultOptions: IntersectionObserverInit = {
    root: null,
    rootMargin: '50px',
    threshold: 0.1,
    ...options,
  };

  return new IntersectionObserver(callback, defaultOptions);
}

// Preload components for better UX
export function preloadComponent<T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>
): Promise<{ default: T }> {
  return importFn();
}

// Bundle splitting utilities
export const bundleGroups = {
  // Core components that should be loaded immediately
  core: [
    () => import('../components/Layout'),
    () => import('../components/Sidebar'),
    () => import('../context/AppContext'),
  ],
  
  // Feature components that can be lazy loaded
  features: [
    () => import('../components/AppGenerator'),
    () => import('../components/ProjectManager'),
    () => import('../components/ProjectList'),
  ],
  
  // UI components that can be loaded on demand
  ui: [
    () => import('../components/ui/Modal'),
    () => import('../components/ui/ProgressBar'),
    () => import('../components/ui/LoadingSpinner'),
  ],
  
  // Heavy features that should be loaded only when needed
  heavy: [
    () => import('../components/MobilePerformanceDemo'),
    () => import('../components/MemoryManagementDemo'),
    () => import('../services/exportService'),
  ],
};

// Preload strategy based on user interaction
export class PreloadStrategy {
  private preloadedComponents = new Set<string>();
  private preloadQueue: Array<() => Promise<any>> = [];
  private isPreloading = false;

  constructor() {
    // Preload on user interaction
    this.setupInteractionPreloading();
  }

  private setupInteractionPreloading() {
    const events = ['mouseenter', 'touchstart', 'focus'];
    
    events.forEach(event => {
      document.addEventListener(event, this.handleUserInteraction, { 
        passive: true,
        once: true 
      });
    });
  }

  private handleUserInteraction = () => {
    // Start preloading non-critical components after user interaction
    this.preloadBundle('features');
  };

  async preloadBundle(bundleName: keyof typeof bundleGroups) {
    if (this.preloadedComponents.has(bundleName) || this.isPreloading) {
      return;
    }

    this.isPreloading = true;
    this.preloadedComponents.add(bundleName);

    const bundle = bundleGroups[bundleName];
    
    try {
      // Use requestIdleCallback to preload during idle time
      await new Promise<void>((resolve) => {
        const preloadNext = async (index = 0) => {
          if (index >= bundle.length) {
            resolve();
            return;
          }

          try {
            await bundle[index]();
          } catch (error) {
            console.warn(`Failed to preload component ${index} in bundle ${bundleName}:`, error);
          }

          if ('requestIdleCallback' in window) {
            requestIdleCallback(() => preloadNext(index + 1));
          } else {
            setTimeout(() => preloadNext(index + 1), 0);
          }
        };

        preloadNext();
      });
    } finally {
      this.isPreloading = false;
    }
  }

  cleanup() {
    const events = ['mouseenter', 'touchstart', 'focus'];
    events.forEach(event => {
      document.removeEventListener(event, this.handleUserInteraction);
    });
  }
}