// Core type definitions for the application

// Re-export all types from individual modules
export * from './user';
export * from './project';
export * from './api';

// Utility types
export type SortOption = 'name' | 'date' | 'status' | 'progress';
export type FilterOption = 'all' | 'active' | 'completed' | 'draft' | 'archived';

// Application state and context types
export interface FilterOptions {
  status: ProjectStatus[];
  type: ProjectType[];
  priority: Priority[];
  complexity: Complexity[];
  tags: string[];
  dateRange?: {
    start: string;
    end: string;
  };
}

export interface GenerationOptions {
  template?: string;
  customizations?: Record<string, any>;
  targetPlatform?: string[];
  includeTests?: boolean;
  includeDocumentation?: boolean;
}

export interface AppState {
  // User data
  user: User | null;
  
  // Project management
  projects: Project[];
  currentProject: Project | null;
  projectsLoading: boolean;
  
  // UI state
  sidebarOpen: boolean;
  activeModal: string | null;
  theme: 'light' | 'dark' | 'system';
  
  // Generation state
  isGenerating: boolean;
  generationProgress: number;
  generationStatus: string;
  generationError: string | null;
  generationOptions: GenerationOptions;
  
  // Search and filters
  searchQuery: string;
  activeFilters: FilterOptions;
  searchResults: Project[];
  searchLoading: boolean;
  
  // Real-time updates
  lastSyncTime: string | null;
  pendingChanges: string[];
  optimisticUpdates: Record<string, Partial<Project>>;
  
  // Loading and error states
  isLoading: boolean;
  error: string | null;
  
  // Data persistence
  storageQuota: number;
  storageUsed: number;
  syncStatus: 'idle' | 'syncing' | 'error';
}

// Legacy interface for backward compatibility
export interface AppContext extends AppState {}

// Action types for state management
export interface AppActions {
  // User actions
  setUser: (user: User | null) => void;
  
  // Project management actions
  addProject: (project: Omit<Project, 'id' | 'createdAt'>) => void;
  updateProject: (id: string, updates: Partial<Project>) => void;
  deleteProject: (id: string) => void;
  setCurrentProject: (project: Project | null) => void;
  setProjectsLoading: (loading: boolean) => void;
  
  // Optimistic updates
  addOptimisticUpdate: (id: string, updates: Partial<Project>) => void;
  removeOptimisticUpdate: (id: string) => void;
  clearOptimisticUpdates: () => void;
  
  // UI state actions
  toggleSidebar: () => void;
  setSidebarOpen: (open: boolean) => void;
  setActiveModal: (modal: string | null) => void;
  setTheme: (theme: 'light' | 'dark' | 'system') => void;
  
  // Generation actions
  setGenerating: (generating: boolean) => void;
  setGenerationProgress: (progress: number) => void;
  setGenerationStatus: (status: string) => void;
  setGenerationError: (error: string | null) => void;
  setGenerationOptions: (options: GenerationOptions) => void;
  
  // Search and filter actions
  setSearchQuery: (query: string) => void;
  setActiveFilters: (filters: FilterOptions) => void;
  setSearchResults: (results: Project[]) => void;
  setSearchLoading: (loading: boolean) => void;
  
  // Real-time update actions
  setLastSyncTime: (time: string) => void;
  addPendingChange: (changeId: string) => void;
  removePendingChange: (changeId: string) => void;
  clearPendingChanges: () => void;
  
  // Storage actions
  setStorageQuota: (quota: number) => void;
  setStorageUsed: (used: number) => void;
  setSyncStatus: (status: 'idle' | 'syncing' | 'error') => void;
  
  // General loading and error actions
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // Refresh actions
  refreshProjects: () => Promise<void>;
}