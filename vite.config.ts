import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  optimizeDeps: {
    exclude: ['lucide-react'],
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          // Core React libraries
          'react-vendor': ['react', 'react-dom'],
          
          // Router
          'router': ['react-router-dom'],
          
          // Animation libraries
          'animations': ['framer-motion'],
          
          // DnD libraries
          'dnd': ['@dnd-kit/core', '@dnd-kit/sortable', '@dnd-kit/utilities'],
          
          // UI libraries
          'ui-vendor': ['lucide-react'],
          
          // Heavy features
          'performance-demo': ['./src/components/MobilePerformanceDemo.tsx'],
          'memory-demo': ['./src/components/MemoryManagementDemo.tsx'],
          
          // Services
          'services': [
            './src/services/appGenerator.ts',
            './src/services/exportService.ts',
            './src/services/projectCRUD.ts',
          ],
        },
      },
    },
    // Enable code splitting
    chunkSizeWarningLimit: 1000,
    // Optimize for modern browsers
    target: 'es2020',
    // Enable source maps for debugging
    sourcemap: true,
  },
  // Optimize dev server
  server: {
    hmr: {
      overlay: false,
    },
  },
});
