import React, { useState, useMemo, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragStartEvent,
  DragOverlay,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import { useApp } from '../context/AppContext';
import { Project } from '../types/project';
import SortableProjectCard from './SortableProjectCard';
import ProjectCard from './ProjectCard';
import ProjectDetailView from './ProjectDetailView';
import { VirtualScrollList } from './ui/VirtualScrollList';
import { ComponentErrorBoundary } from './error/ComponentErrorBoundary';
import { useProgressiveLoading } from '../hooks/useProgressiveLoading';
import { animationVariants } from '../utils/animations';

interface ProjectListProps {
  projects?: Project[];
  showDragHandle?: boolean;
  enableReordering?: boolean;
  onProjectClick?: (project: Project) => void;
  onProjectAction?: (projectId: string, action: 'star' | 'archive' | 'delete' | 'duplicate' | 'edit') => void;
  variant?: 'default' | 'compact' | 'detailed';
  enableDetailView?: boolean;
  className?: string;
  enableVirtualScrolling?: boolean;
  virtualScrollHeight?: number;
  itemHeight?: number;
  enableProgressiveLoading?: boolean;
  batchSize?: number;
}

const ProjectList: React.FC<ProjectListProps> = ({
  projects: propProjects,
  showDragHandle = true,
  enableReordering = true,
  onProjectClick,
  onProjectAction,
  variant = 'default',
  enableDetailView = false,
  className,
  enableVirtualScrolling = false,
  virtualScrollHeight = 600,
  itemHeight = 120,
  enableProgressiveLoading = false,
  batchSize = 50,
}) => {
  const { projects: contextProjects, updateProject } = useApp();
  const [activeId, setActiveId] = useState<string | null>(null);
  const [draggedProject, setDraggedProject] = useState<Project | null>(null);
  const [expandedProjects, setExpandedProjects] = useState<Set<string>>(new Set());

  // Use provided projects or context projects
  const allProjects = propProjects || contextProjects;

  // Progressive loading for large datasets
  const {
    loadedItems: projects,
    isLoading: isLoadingMore,
    hasMore,
    loadNextBatch,
  } = useProgressiveLoading({
    data: allProjects,
    batchSize,
    autoLoad: enableProgressiveLoading,
  });

  // Use all projects if progressive loading is disabled
  const displayProjects = enableProgressiveLoading ? projects : allProjects;

  // Create sensors for drag and drop
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Get project IDs for sortable context
  const projectIds = useMemo(() => displayProjects.map(project => project.id), [displayProjects]);

  // Handle drag start
  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    setActiveId(active.id as string);
    
    const project = displayProjects.find(p => p.id === active.id);
    setDraggedProject(project || null);
  };

  // Handle drag end
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      const oldIndex = displayProjects.findIndex(project => project.id === active.id);
      const newIndex = displayProjects.findIndex(project => project.id === over?.id);

      if (oldIndex !== -1 && newIndex !== -1) {
        const reorderedProjects = arrayMove(displayProjects, oldIndex, newIndex);
        
        // Update the order in context - you might want to add a reorderProjects action
        // For now, we'll update each project with a new order field
        reorderedProjects.forEach((project, index) => {
          updateProject(project.id, { ...project, order: index });
        });
      }
    }

    setActiveId(null);
    setDraggedProject(null);
  };

  // Handle project actions
  const handleProjectAction = (projectId: string, action: 'star' | 'archive' | 'delete' | 'duplicate') => {
    if (onProjectAction) {
      onProjectAction(projectId, action);
      return;
    }

    // Default action handling
    const project = displayProjects.find(p => p.id === projectId);
    if (!project) return;

    switch (action) {
      case 'star':
        updateProject(projectId, { isStarred: !project.isStarred });
        break;
      case 'archive':
        updateProject(projectId, { status: project.status === 'archived' ? 'draft' : 'archived' });
        break;
      case 'delete':
        // This should probably show a confirmation modal
        // For now, just update status
        updateProject(projectId, { status: 'archived' });
        break;
      case 'duplicate':
        // This would need to be implemented in the context
        console.log('Duplicate project:', projectId);
        break;
    }
  };

  // Handle project click
  const handleProjectClick = (project: Project) => {
    if (enableDetailView) {
      toggleProjectExpansion(project.id);
    } else if (onProjectClick) {
      onProjectClick(project);
    }
  };

  // Handle project expansion
  const toggleProjectExpansion = (projectId: string) => {
    setExpandedProjects(prev => {
      const newSet = new Set(prev);
      if (newSet.has(projectId)) {
        newSet.delete(projectId);
      } else {
        newSet.add(projectId);
      }
      return newSet;
    });
  };

  // Render project item for virtual scrolling
  const renderProjectItem = useCallback((project: Project, index: number) => {
    return (
      <div key={project.id} className="px-4">
        {enableDetailView ? (
          <ProjectDetailView
            project={project}
            isExpanded={expandedProjects.has(project.id)}
            onToggleExpanded={() => toggleProjectExpansion(project.id)}
            onAction={handleProjectAction}
          />
        ) : enableReordering ? (
          <SortableProjectCard
            project={project}
            showDragHandle={showDragHandle}
            onClick={() => handleProjectClick(project)}
            onAction={handleProjectAction}
            isDragging={activeId === project.id}
            variant={variant}
          />
        ) : (
          <ProjectCard
            project={project}
            onClick={() => handleProjectClick(project)}
            onAction={handleProjectAction}
            variant={variant}
          />
        )}
      </div>
    );
  }, [
    enableDetailView,
    enableReordering,
    expandedProjects,
    showDragHandle,
    activeId,
    variant,
    handleProjectClick,
    handleProjectAction,
    toggleProjectExpansion,
  ]);

  if (displayProjects.length === 0 && !isLoadingMore) {
    return (
      <motion.div
        variants={animationVariants.fadeIn}
        initial="initial"
        animate="animate"
        className="flex flex-col items-center justify-center py-12 text-center"
      >
        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
          <div className="w-8 h-8 bg-gray-300 rounded-lg" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No projects yet</h3>
        <p className="text-gray-500 max-w-sm">
          Create your first project to get started with the AI App Generator.
        </p>
      </motion.div>
    );
  }

  // Virtual scrolling for large datasets
  if (enableVirtualScrolling && displayProjects.length > 20) {
    return (
      <div className={className}>
        <VirtualScrollList
          items={displayProjects}
          itemHeight={itemHeight}
          height={virtualScrollHeight}
          renderItem={renderProjectItem}
          loadMore={enableProgressiveLoading && hasMore ? loadNextBatch : undefined}
          hasMore={hasMore}
          isLoading={isLoadingMore}
          className="space-y-2"
        />
      </div>
    );
  }

  // Regular rendering for smaller datasets
  return (
    <div className={className}>
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
        modifiers={enableReordering ? [restrictToVerticalAxis] : []}
      >
        <SortableContext 
          items={projectIds} 
          strategy={verticalListSortingStrategy}
          disabled={!enableReordering}
        >
          <motion.div
            variants={animationVariants.stagger}
            initial="initial"
            animate="animate"
            className="space-y-4"
          >
            <AnimatePresence mode="popLayout">
              {displayProjects.map((project, index) => (
                <motion.div
                  key={project.id}
                  variants={animationVariants.slideUp}
                  initial="initial"
                  animate="animate"
                  exit="exit"
                  transition={{
                    duration: 0.3,
                    delay: index * 0.05,
                    ease: 'easeOut'
                  }}
                  layout
                >
                  {renderProjectItem(project, index)}
                </motion.div>
              ))}
            </AnimatePresence>
          </motion.div>

          {/* Load more button for progressive loading */}
          {enableProgressiveLoading && hasMore && (
            <motion.div
              variants={animationVariants.fadeIn}
              initial="initial"
              animate="animate"
              className="flex justify-center pt-6"
            >
              <button
                onClick={loadNextBatch}
                disabled={isLoadingMore}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {isLoadingMore ? 'Loading...' : `Load More (${allProjects.length - displayProjects.length} remaining)`}
              </button>
            </motion.div>
          )}
        </SortableContext>

        {/* Drag Overlay */}
        <DragOverlay>
          {activeId && draggedProject ? (
            <ProjectCard
              project={draggedProject}
              onClick={() => {}}
              onAction={() => {}}
              isDragging={true}
              variant={variant}
            />
          ) : null}
        </DragOverlay>
      </DndContext>
    </div>
  );
};

export default ProjectList;