import React, { useState, useCallback, useMemo } from 'react';
import { motion } from 'framer-motion';
import { 
  Star, 
  Archive, 
  Trash2, 
  Copy, 
  MoreHorizontal, 
  Calendar,
  Clock,
  Tag,
  TrendingUp,
  AlertCircle,
  Edit
} from 'lucide-react';
import { Link } from 'react-router-dom';
import { Project } from '../types/project';
import { getStatusStyles, getPriorityStyles, truncateText, formatDate } from '../utils/helpers';
import { ProjectCRUDService } from '../services/projectCRUD';
import { useApp } from '../context/AppContext';
import { useResponsiveLayout } from '../hooks/useResponsiveLayout';
import AnimatedCard from './ui/AnimatedCard';
import SwipeableCard, { commonSwipeActions } from './ui/SwipeableCard';
import Button from './ui/Button';
import Badge from './ui/Badge';
import { cn } from '../utils/helpers';
import { measureComponentRender } from '../utils/performanceMonitoring';

interface ProjectCardProps {
  project: Project;
  onClick: () => void;
  onAction: (projectId: string, action: 'star' | 'archive' | 'delete' | 'duplicate' | 'edit') => void;
  isDragging?: boolean;
  showActions?: boolean;
  variant?: 'default' | 'compact' | 'detailed';
  className?: string;
}

const ProjectCard: React.FC<ProjectCardProps> = React.memo(({
  project,
  onClick,
  onAction,
  isDragging = false,
  showActions = true,
  variant = 'default',
  className
}) => {
  const { updateProject, addOptimisticUpdate, removeOptimisticUpdate } = useApp();
  const { isMobile } = useResponsiveLayout();
  const [isHovered, setIsHovered] = useState(false);
  const [showMenu, setShowMenu] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);

  // Memoize expensive calculations
  const statusStyles = useMemo(() => getStatusStyles(project.status), [project.status]);
  const priorityColor = useMemo(() => getPriorityStyles(project.priority), [project.priority]);
  const projectTypeStyles = useMemo(() => {
    const styles = {
      mobile: 'bg-green-100 text-green-700 border-green-200',
      web: 'bg-blue-100 text-blue-700 border-blue-200',
      form: 'bg-purple-100 text-purple-700 border-purple-200',
      ai: 'bg-orange-100 text-orange-700 border-orange-200',
      ecommerce: 'bg-pink-100 text-pink-700 border-pink-200'
    };
    return styles[project.type] || styles.web;
  }, [project.type]);

  // Memoize formatted dates
  const formattedDates = useMemo(() => ({
    created: formatDate(project.createdAt),
    modified: formatDate(project.lastModified)
  }), [project.createdAt, project.lastModified]);

  // Memoize truncated description
  const truncatedDescription = useMemo(() => 
    truncateText(project.description, variant === 'compact' ? 80 : 120), 
    [project.description, variant]
  );

  // Memoize action handlers to prevent unnecessary re-renders
  const handleAction = useCallback(async (e: React.MouseEvent, action: 'star' | 'archive' | 'delete' | 'duplicate' | 'edit') => {
    e.preventDefault();
    e.stopPropagation();
    
    if (action === 'star') {
      // Handle starring with optimistic update
      setIsUpdating(true);
      const newStarredState = !project.isStarred;
      
      try {
        await ProjectCRUDService.updateProject(
          { id: project.id, isStarred: newStarredState },
          // Optimistic update
          (id, updates) => {
            addOptimisticUpdate(id, updates);
            updateProject(id, updates);
          },
          // Success
          () => {
            removeOptimisticUpdate(project.id);
          },
          // Error
          (error) => {
            console.error('Failed to update star status:', error);
            // Revert optimistic update
            updateProject(project.id, { isStarred: project.isStarred });
            removeOptimisticUpdate(project.id);
          }
        );
      } finally {
        setIsUpdating(false);
      }
    } else if (action === 'archive') {
      // Handle archiving with optimistic update
      setIsUpdating(true);
      const newStatus = project.status === 'archived' ? 'draft' : 'archived';
      
      try {
        await ProjectCRUDService.updateProject(
          { id: project.id, status: newStatus },
          // Optimistic update
          (id, updates) => {
            addOptimisticUpdate(id, updates);
            updateProject(id, updates);
          },
          // Success
          () => {
            removeOptimisticUpdate(project.id);
          },
          // Error
          (error) => {
            console.error('Failed to update archive status:', error);
            // Revert optimistic update
            updateProject(project.id, { status: project.status });
            removeOptimisticUpdate(project.id);
          }
        );
      } finally {
        setIsUpdating(false);
      }
    } else {
      // For other actions, delegate to parent
      onAction(project.id, action);
    }
    
    setShowMenu(false);
  }, [project.id, project.isStarred, project.status, updateProject, addOptimisticUpdate, removeOptimisticUpdate, onAction]);

  // Memoize menu toggle handler
  const handleMenuToggle = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setShowMenu(!showMenu);
  }, [showMenu]);

  // Memoize hover handlers
  const handleMouseEnter = useCallback(() => setIsHovered(true), []);
  const handleMouseLeave = useCallback(() => setIsHovered(false), []);

  // Memoize Progress bar component
  const ProgressBar = useMemo(() => (
    <div className="flex items-center gap-2">
      <div className="flex-1 h-2 bg-gray-200 rounded-full overflow-hidden">
        <motion.div
          className={cn(
            'h-full rounded-full transition-all duration-500',
            project.status === 'completed' ? 'bg-green-500' :
            project.status === 'error' ? 'bg-red-500' :
            project.status === 'active' ? 'bg-blue-500' : 'bg-gray-400'
          )}
          initial={{ width: 0 }}
          animate={{ width: `${project.progress}%` }}
          transition={{ duration: 0.8, ease: 'easeOut' }}
        />
      </div>
      <span className="text-xs text-gray-500 font-medium min-w-[3rem] text-right">
        {project.progress}%
      </span>
    </div>
  ), [project.progress, project.status]);

  // Memoize Action buttons
  const ActionButtons = useMemo(() => (
    <div className="flex items-center gap-1">
      <motion.button
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        onClick={(e) => handleAction(e, 'star')}
        disabled={isUpdating}
        className={cn(
          'p-1.5 rounded-lg transition-all duration-200',
          'hover:bg-gray-100 active:bg-gray-200 disabled:opacity-50',
          project.isStarred ? 'text-yellow-500' : 'text-gray-400 hover:text-yellow-500'
        )}
        aria-label={project.isStarred ? 'Remove from favorites' : 'Add to favorites'}
      >
        <Star className={cn('w-4 h-4', project.isStarred && 'fill-current')} />
      </motion.button>

      <motion.button
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        onClick={(e) => handleAction(e, 'edit')}
        className="p-1.5 rounded-lg text-gray-400 hover:text-green-500 hover:bg-gray-100 active:bg-gray-200 transition-all duration-200"
        aria-label="Edit project"
      >
        <Edit className="w-4 h-4" />
      </motion.button>

      <motion.button
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        onClick={(e) => handleAction(e, 'duplicate')}
        className="p-1.5 rounded-lg text-gray-400 hover:text-blue-500 hover:bg-gray-100 active:bg-gray-200 transition-all duration-200"
        aria-label="Duplicate project"
      >
        <Copy className="w-4 h-4" />
      </motion.button>

      <motion.button
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        onClick={(e) => handleAction(e, 'archive')}
        disabled={isUpdating}
        className="p-1.5 rounded-lg text-gray-400 hover:text-orange-500 hover:bg-gray-100 active:bg-gray-200 transition-all duration-200 disabled:opacity-50"
        aria-label={project.status === 'archived' ? 'Restore project' : 'Archive project'}
      >
        <Archive className="w-4 h-4" />
      </motion.button>

      <motion.button
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        onClick={(e) => handleAction(e, 'delete')}
        className="p-1.5 rounded-lg text-gray-400 hover:text-red-500 hover:bg-gray-100 active:bg-gray-200 transition-all duration-200"
        aria-label="Delete project"
      >
        <Trash2 className="w-4 h-4" />
      </motion.button>
    </div>
  ), [handleAction, project.isStarred, isUpdating, project.status]);

  // Memoize compact content
  const CompactContent = useMemo(() => (
      <div className="flex items-start gap-3 p-3">
        <div className="relative">
          <div className="p-2 bg-gray-800/50 rounded-lg group-hover:bg-gray-700/50 transition-all duration-200">
            <div className="w-4 h-4 text-gray-400 group-hover:text-white transition-all duration-200 bg-current rounded-sm" />
          </div>
          <div className={cn(
            'absolute -bottom-1 -right-1 w-3 h-3 rounded-full shadow-lg transition-all duration-200',
            statusStyles.dot,
            isHovered ? 'scale-125' : ''
          )} />
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between mb-1">
            <div className="flex items-center gap-2 flex-1 min-w-0">
              <span className="text-sm font-medium leading-tight block group-hover:text-white transition-colors truncate text-gray-300">
                {project.name}
              </span>
              {project.isStarred && (
                <Star className="w-3 h-3 text-yellow-400 fill-current flex-shrink-0" />
              )}
            </div>
            
            <div className="flex items-center gap-1 ml-2">
              <div className={cn('w-2 h-2 rounded-full opacity-60', priorityColor.replace('text-', 'bg-'))} />
              {isHovered && showActions && (
                <div className="flex items-center gap-1">
                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={(e) => handleAction(e, 'star')}
                    className="p-1 hover:bg-gray-600/50 rounded transition-colors"
                    aria-label="Toggle star"
                  >
                    <Star className="w-3 h-3 text-gray-400 hover:text-yellow-400" />
                  </motion.button>
                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={(e) => handleAction(e, 'archive')}
                    className="p-1 hover:bg-gray-600/50 rounded transition-colors"
                    aria-label="Archive project"
                  >
                    <Archive className="w-3 h-3 text-gray-400 hover:text-blue-400" />
                  </motion.button>
                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={(e) => handleAction(e, 'delete')}
                    className="p-1 hover:bg-gray-600/50 rounded transition-colors"
                    aria-label="Delete project"
                  >
                    <Trash2 className="w-3 h-3 text-gray-400 hover:text-red-400" />
                  </motion.button>
                </div>
              )}
            </div>
          </div>
          
          <p className="text-xs text-gray-500 leading-relaxed mb-2 line-clamp-2">
            {truncatedDescription}
          </p>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Badge 
                variant={
                  project.status === 'active' ? 'success' : 
                  project.status === 'completed' ? 'info' : 
                  project.status === 'error' ? 'error' : 'warning'
                } 
                size="sm"
              >
                {project.status}
              </Badge>
              {project.progress > 0 && (
                <div className="flex items-center gap-1">
                  <div className="w-12 h-1 bg-gray-700 rounded-full overflow-hidden">
                    <motion.div 
                      className="h-full bg-blue-400 rounded-full transition-all duration-300"
                      initial={{ width: 0 }}
                      animate={{ width: `${project.progress}%` }}
                      transition={{ duration: 0.8, ease: 'easeOut' }}
                    />
                  </div>
                  <span className="text-xs text-gray-500">{project.progress}%</span>
                </div>
              )}
            </div>
            
            <span className="text-xs text-gray-500">{formatDate(project.lastModified)}</span>
          </div>

          {project.tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mt-2">
              {project.tags.slice(0, 2).map((tag, index) => (
                <span
                  key={index}
                  className="px-2 py-0.5 bg-gray-700/30 text-gray-400 rounded text-xs"
                >
                  {tag}
                </span>
              ))}
              {project.tags.length > 2 && (
                <span className="px-2 py-0.5 bg-gray-700/30 text-gray-400 rounded text-xs">
                  +{project.tags.length - 2}
                </span>
              )}
            </div>
          )}
        </div>
      </div>
    ), [
      project.name, 
      project.description, 
      project.isStarred, 
      project.status, 
      project.progress, 
      project.tags, 
      project.lastModified,
      statusStyles,
      priorityColor,
      isHovered,
      showActions,
      handleAction,
      truncatedDescription,
      formattedDates
    ]);

  // Compact variant for sidebar
  if (variant === 'compact') {
    // Memoize content wrapper
    const compactContent = useMemo(() => project.route ? (
      <Link to={project.route} className="block">
        {CompactContent}
      </Link>
    ) : (
      <div onClick={onClick} className="cursor-pointer">
        {CompactContent}
      </div>
    ), [project.route, CompactContent, onClick]);

    return (
      <div
        role="listitem"
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        className={cn(
          'group relative text-gray-300 hover:bg-gray-800/50 rounded-xl cursor-pointer transition-all duration-300 border border-transparent hover:border-gray-700/50',
          isDragging && 'bg-gradient-to-r from-blue-500/10 to-purple-500/10 border-blue-500/20 scale-105 rotate-1',
          className
        )}
      >
        {compactContent}
        
        {/* Hover Glow Effect */}
        {isHovered && (
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-xl border border-blue-500/20 transition-all duration-300" />
        )}
      </div>
    );
  }

  // Memoize CardContent for default and detailed variants
  const CardContent = useMemo(() => (
    <div className={cn('p-6', isMobile && 'p-4')}>
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-start gap-3 flex-1 min-w-0">
          {/* Project Icon */}
          <div className={cn(
            'w-12 h-12 rounded-xl border-2 flex items-center justify-center flex-shrink-0',
            getProjectTypeStyles(),
            isMobile && 'w-10 h-10'
          )}>
            <div className={cn('w-6 h-6 bg-current rounded opacity-80', isMobile && 'w-5 h-5')} />
          </div>

          {/* Project Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <h3 className={cn('font-semibold text-gray-900 truncate', isMobile && 'text-sm')}>
                {project.name}
              </h3>
              {project.isStarred && (
                <Star className="w-4 h-4 text-yellow-500 fill-current flex-shrink-0" />
              )}
            </div>
            <p className={cn('text-sm text-gray-600 line-clamp-2 mb-2', isMobile && 'text-xs')}>
              {project.description}
            </p>
            
            {/* Status and Priority */}
            <div className="flex items-center gap-2">
              <Badge 
                variant={
                  project.status === 'active' ? 'success' : 
                  project.status === 'completed' ? 'info' : 
                  project.status === 'error' ? 'error' : 'warning'
                } 
                size="sm"
              >
                {project.status}
              </Badge>
              <div className={cn('w-2 h-2 rounded-full', priorityColor.replace('text-', 'bg-'))} />
              <span className={cn('text-xs text-gray-500 capitalize', isMobile && 'text-xs')}>
                {project.priority}
              </span>
            </div>
          </div>
        </div>

        {/* Actions - Hide on mobile (use swipe instead) */}
        {showActions && !isMobile && (
          <div className={cn(
            'flex items-center gap-1 transition-opacity duration-200',
            isHovered ? 'opacity-100' : 'opacity-0'
          )}>
            <ActionButtons />
          </div>
        )}
      </div>

      {/* Progress */}
      {project.progress > 0 && (
        <div className="mb-4">
          <ProgressBar />
        </div>
      )}

      {/* Tags */}
      {project.tags.length > 0 && (
        <div className="flex flex-wrap gap-1 mb-4">
          {project.tags.slice(0, 3).map((tag, index) => (
            <span
              key={index}
              className="inline-flex items-center gap-1 px-2 py-1 bg-gray-100 text-gray-700 rounded-md text-xs"
            >
              <Tag className="w-3 h-3" />
              {tag}
            </span>
          ))}
          {project.tags.length > 3 && (
            <span className="px-2 py-1 bg-gray-100 text-gray-500 rounded-md text-xs">
              +{project.tags.length - 3} more
            </span>
          )}
        </div>
      )}

      {/* Footer */}
      <div className="flex items-center justify-between text-xs text-gray-500">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-1">
            <Calendar className="w-3 h-3" />
            <span>Created {formattedDates.created}</span>
          </div>
          <div className="flex items-center gap-1">
            <Clock className="w-3 h-3" />
            <span>Updated {formattedDates.modified}</span>
          </div>
        </div>
        
        {project.complexity && (
          <div className="flex items-center gap-1">
            <TrendingUp className="w-3 h-3" />
            <span className="capitalize">{project.complexity}</span>
          </div>
        )}
      </div>

      {/* Error state indicator */}
      {project.status === 'error' && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-3 p-2 bg-red-50 border border-red-200 rounded-lg flex items-center gap-2"
        >
          <AlertCircle className="w-4 h-4 text-red-500 flex-shrink-0" />
          <span className="text-sm text-red-700">This project has encountered an error</span>
        </motion.div>
      )}
    </div>
  ), [isMobile, project.name, project.isStarred, project.description, project.status, project.priority, project.progress, project.tags, project.complexity, priorityColor, showActions, isHovered, ActionButtons, ProgressBar, formattedDates.created, formattedDates.modified]);

  // Memoize swipe actions for mobile
  const swipeActions = useMemo(() => ({
    left: [
      commonSwipeActions.star(() => handleAction(new MouseEvent('click') as any, 'star')),
      commonSwipeActions.edit(() => handleAction(new MouseEvent('click') as any, 'edit'))
    ],
    right: [
      commonSwipeActions.archive(() => handleAction(new MouseEvent('click') as any, 'archive')),
      commonSwipeActions.delete(() => handleAction(new MouseEvent('click') as any, 'delete'))
    ]
  }), [handleAction]);

  // Memoize content wrapper
  const content = useMemo(() => project.route ? (
    <Link to={project.route} className="block">
      {CardContent}
    </Link>
  ) : (
    <div onClick={onClick} className="cursor-pointer">
      {CardContent}
    </div>
  ), [project.route, CardContent, onClick]);

  // For mobile, wrap with SwipeableCard
  if (isMobile && showActions) {
    return (
      <SwipeableCard
        leftActions={swipeActions.left}
        rightActions={swipeActions.right}
        className={cn(
          'transition-all duration-200',
          isDragging && 'rotate-1 scale-105',
          className
        )}
        contentClassName="bg-white border border-gray-200 rounded-xl shadow-sm"
      >
        {content}
      </SwipeableCard>
    );
  }

  return (
    <AnimatedCard
      variant={isDragging ? 'glow' : 'hover'}
      onHover={setIsHovered}
      isDragging={isDragging}
      className={cn(
        'transition-all duration-200',
        isDragging && 'rotate-1 scale-105',
        className
      )}
    >
      {content}
    </AnimatedCard>
  );

}, (prevProps, nextProps) => {
  // Custom comparison function for React.memo
  return (
    prevProps.project.id === nextProps.project.id &&
    prevProps.project.name === nextProps.project.name &&
    prevProps.project.description === nextProps.project.description &&
    prevProps.project.status === nextProps.project.status &&
    prevProps.project.progress === nextProps.project.progress &&
    prevProps.project.isStarred === nextProps.project.isStarred &&
    prevProps.project.lastModified === nextProps.project.lastModified &&
    prevProps.project.tags.length === nextProps.project.tags.length &&
    prevProps.project.tags.every((tag, index) => tag === nextProps.project.tags[index]) &&
    prevProps.isDragging === nextProps.isDragging &&
    prevProps.showActions === nextProps.showActions &&
    prevProps.variant === nextProps.variant &&
    prevProps.className === nextProps.className
  );
});

ProjectCard.displayName = 'ProjectCard';

export default measureComponentRender('ProjectCard')(ProjectCard);